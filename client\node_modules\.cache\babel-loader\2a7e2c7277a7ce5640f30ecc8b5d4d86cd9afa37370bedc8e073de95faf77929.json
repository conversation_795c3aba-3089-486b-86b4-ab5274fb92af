{"ast": null, "code": "var _jsxFileName = \"E:\\\\kumaran\\\\botnexus\\\\client\\\\src\\\\components\\\\ChatInput.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChatInput = ({\n  onSendMessage,\n  loading,\n  conversationalFlow,\n  hybridFlow,\n  onClearChat\n}) => {\n  _s();\n  const [message, setMessage] = useState('');\n  const handleSubmit = e => {\n    e.preventDefault();\n    if (message.trim() && !loading) {\n      onSendMessage(message);\n      setMessage('');\n    }\n  };\n  const handleCancel = () => {\n    onSendMessage('cancel');\n    setMessage('');\n  };\n  const handleClearChat = () => {\n    if (onClearChat && !loading) {\n      onClearChat();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [onClearChat && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-end p-2 border-t border-gray-100\",\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"button\",\n        onClick: handleClearChat,\n        className: \"flex items-center space-x-1 px-3 py-1 text-sm text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-md transition-colors\",\n        disabled: loading,\n        title: \"Clear chat history\",\n        children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n          xmlns: \"http://www.w3.org/2000/svg\",\n          className: \"h-4 w-4\",\n          fill: \"none\",\n          viewBox: \"0 0 24 24\",\n          stroke: \"currentColor\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Clear Chat\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      className: \"flex items-center border-t border-gray-200 p-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        value: message,\n        onChange: e => setMessage(e.target.value),\n        placeholder: conversationalFlow && conversationalFlow.isActive ? \"Type your answer or 'cancel' to exit the form...\" : hybridFlow && hybridFlow.isActive && hybridFlow.isConversationalPhase ? \"🔄 Type your answer or 'cancel' to exit the hybrid form...\" : \"Ask a question...\",\n        className: \"flex-1 p-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n        disabled: loading\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this), (conversationalFlow && conversationalFlow.isActive || hybridFlow && hybridFlow.isActive && hybridFlow.isConversationalPhase) && /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"button\",\n        onClick: handleCancel,\n        className: \"bg-red-500 hover:bg-red-600 text-white px-3 py-2 transition-colors\",\n        disabled: loading,\n        children: \"Cancel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        className: `bg-blue-500 text-white p-2 ${conversationalFlow && conversationalFlow.isActive || hybridFlow && hybridFlow.isActive && hybridFlow.isConversationalPhase ? 'rounded-r-md' : 'rounded-r-md'} ${loading ? 'opacity-50 cursor-not-allowed' : 'hover:bg-blue-600'}`,\n        disabled: loading,\n        children: loading ? /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"animate-spin h-5 w-5 text-white\",\n          xmlns: \"http://www.w3.org/2000/svg\",\n          fill: \"none\",\n          viewBox: \"0 0 24 24\",\n          children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n            className: \"opacity-25\",\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            stroke: \"currentColor\",\n            strokeWidth: \"4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n            className: \"opacity-75\",\n            fill: \"currentColor\",\n            d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"svg\", {\n          xmlns: \"http://www.w3.org/2000/svg\",\n          className: \"h-5 w-5\",\n          viewBox: \"0 0 20 20\",\n          fill: \"currentColor\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            fillRule: \"evenodd\",\n            d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.707l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z\",\n            clipRule: \"evenodd\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 5\n  }, this);\n};\n_s(ChatInput, \"l8KXAebGu4sZHsyCIQX7P8si41w=\");\n_c = ChatInput;\nexport default ChatInput;\nvar _c;\n$RefreshReg$(_c, \"ChatInput\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "ChatInput", "onSendMessage", "loading", "conversationalFlow", "hybridFlow", "onClearChat", "_s", "message", "setMessage", "handleSubmit", "e", "preventDefault", "trim", "handleCancel", "handleClearChat", "children", "className", "type", "onClick", "disabled", "title", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "value", "onChange", "target", "placeholder", "isActive", "isConversationalPhase", "cx", "cy", "r", "fillRule", "clipRule", "_c", "$RefreshReg$"], "sources": ["E:/kumaran/botnexus/client/src/components/ChatInput.js"], "sourcesContent": ["import React, { useState } from 'react';\r\n\r\nconst ChatInput = ({ onSendMessage, loading, conversationalFlow, hybridFlow, onClearChat }) => {\r\n  const [message, setMessage] = useState('');\r\n\r\n  const handleSubmit = (e) => {\r\n    e.preventDefault();\r\n    if (message.trim() && !loading) {\r\n      onSendMessage(message);\r\n      setMessage('');\r\n    }\r\n  };\r\n\r\n  const handleCancel = () => {\r\n    onSendMessage('cancel');\r\n    setMessage('');\r\n  };\r\n\r\n  const handleClearChat = () => {\r\n    if (onClearChat && !loading) {\r\n      onClearChat();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      {/* Clear chat button */}\r\n      {onClearChat && (\r\n        <div className=\"flex justify-end p-2 border-t border-gray-100\">\r\n          <button\r\n            type=\"button\"\r\n            onClick={handleClearChat}\r\n            className=\"flex items-center space-x-1 px-3 py-1 text-sm text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-md transition-colors\"\r\n            disabled={loading}\r\n            title=\"Clear chat history\"\r\n          >\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\r\n            </svg>\r\n            <span>Clear Chat</span>\r\n          </button>\r\n        </div>\r\n      )}\r\n\r\n      {/* Input form */}\r\n      <form onSubmit={handleSubmit} className=\"flex items-center border-t border-gray-200 p-4\">\r\n        <input\r\n          type=\"text\"\r\n          value={message}\r\n          onChange={(e) => setMessage(e.target.value)}\r\n          placeholder={\r\n            (conversationalFlow && conversationalFlow.isActive) ? \r\n              \"Type your answer or 'cancel' to exit the form...\" : \r\n            (hybridFlow && hybridFlow.isActive && hybridFlow.isConversationalPhase) ? \r\n              \"🔄 Type your answer or 'cancel' to exit the hybrid form...\" : \r\n              \"Ask a question...\"\r\n          }\r\n          className=\"flex-1 p-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n          disabled={loading}\r\n        />\r\n        {/* Show cancel button when in conversational flow or hybrid flow conversational phase */}\r\n        {((conversationalFlow && conversationalFlow.isActive) || \r\n          (hybridFlow && hybridFlow.isActive && hybridFlow.isConversationalPhase)) && (\r\n          <button\r\n            type=\"button\"\r\n            onClick={handleCancel}\r\n            className=\"bg-red-500 hover:bg-red-600 text-white px-3 py-2 transition-colors\"\r\n            disabled={loading}\r\n          >\r\n            Cancel\r\n          </button>\r\n        )}\r\n        <button\r\n          type=\"submit\"\r\n          className={`bg-blue-500 text-white p-2 ${\r\n            (conversationalFlow && conversationalFlow.isActive) || \r\n            (hybridFlow && hybridFlow.isActive && hybridFlow.isConversationalPhase) ? \r\n              'rounded-r-md' : 'rounded-r-md'\r\n          } ${\r\n            loading ? 'opacity-50 cursor-not-allowed' : 'hover:bg-blue-600'\r\n          }`}\r\n          disabled={loading}\r\n        >\r\n          {loading ? (\r\n            <svg className=\"animate-spin h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n              <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\r\n              <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n            </svg>\r\n          ) : (\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n              <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.707l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z\" clipRule=\"evenodd\" />\r\n            </svg>\r\n          )}\r\n        </button>\r\n      </form>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ChatInput;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,SAAS,GAAGA,CAAC;EAAEC,aAAa;EAAEC,OAAO;EAAEC,kBAAkB;EAAEC,UAAU;EAAEC;AAAY,CAAC,KAAK;EAAAC,EAAA;EAC7F,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EAE1C,MAAMY,YAAY,GAAIC,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAIJ,OAAO,CAACK,IAAI,CAAC,CAAC,IAAI,CAACV,OAAO,EAAE;MAC9BD,aAAa,CAACM,OAAO,CAAC;MACtBC,UAAU,CAAC,EAAE,CAAC;IAChB;EACF,CAAC;EAED,MAAMK,YAAY,GAAGA,CAAA,KAAM;IACzBZ,aAAa,CAAC,QAAQ,CAAC;IACvBO,UAAU,CAAC,EAAE,CAAC;EAChB,CAAC;EAED,MAAMM,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIT,WAAW,IAAI,CAACH,OAAO,EAAE;MAC3BG,WAAW,CAAC,CAAC;IACf;EACF,CAAC;EAED,oBACEN,OAAA;IAAAgB,QAAA,GAEGV,WAAW,iBACVN,OAAA;MAAKiB,SAAS,EAAC,+CAA+C;MAAAD,QAAA,eAC5DhB,OAAA;QACEkB,IAAI,EAAC,QAAQ;QACbC,OAAO,EAAEJ,eAAgB;QACzBE,SAAS,EAAC,6HAA6H;QACvIG,QAAQ,EAAEjB,OAAQ;QAClBkB,KAAK,EAAC,oBAAoB;QAAAL,QAAA,gBAE1BhB,OAAA;UAAKsB,KAAK,EAAC,4BAA4B;UAACL,SAAS,EAAC,SAAS;UAACM,IAAI,EAAC,MAAM;UAACC,OAAO,EAAC,WAAW;UAACC,MAAM,EAAC,cAAc;UAAAT,QAAA,eAC/GhB,OAAA;YAAM0B,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC,OAAO;YAACC,WAAW,EAAE,CAAE;YAACC,CAAC,EAAC;UAA8H;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnM,CAAC,eACNjC,OAAA;UAAAgB,QAAA,EAAM;QAAU;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN,eAGDjC,OAAA;MAAMkC,QAAQ,EAAExB,YAAa;MAACO,SAAS,EAAC,gDAAgD;MAAAD,QAAA,gBACtFhB,OAAA;QACEkB,IAAI,EAAC,MAAM;QACXiB,KAAK,EAAE3B,OAAQ;QACf4B,QAAQ,EAAGzB,CAAC,IAAKF,UAAU,CAACE,CAAC,CAAC0B,MAAM,CAACF,KAAK,CAAE;QAC5CG,WAAW,EACRlC,kBAAkB,IAAIA,kBAAkB,CAACmC,QAAQ,GAChD,kDAAkD,GACnDlC,UAAU,IAAIA,UAAU,CAACkC,QAAQ,IAAIlC,UAAU,CAACmC,qBAAqB,GACpE,4DAA4D,GAC5D,mBACH;QACDvB,SAAS,EAAC,oGAAoG;QAC9GG,QAAQ,EAAEjB;MAAQ;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC,EAED,CAAE7B,kBAAkB,IAAIA,kBAAkB,CAACmC,QAAQ,IACjDlC,UAAU,IAAIA,UAAU,CAACkC,QAAQ,IAAIlC,UAAU,CAACmC,qBAAsB,kBACvExC,OAAA;QACEkB,IAAI,EAAC,QAAQ;QACbC,OAAO,EAAEL,YAAa;QACtBG,SAAS,EAAC,oEAAoE;QAC9EG,QAAQ,EAAEjB,OAAQ;QAAAa,QAAA,EACnB;MAED;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT,eACDjC,OAAA;QACEkB,IAAI,EAAC,QAAQ;QACbD,SAAS,EAAE,8BACRb,kBAAkB,IAAIA,kBAAkB,CAACmC,QAAQ,IACjDlC,UAAU,IAAIA,UAAU,CAACkC,QAAQ,IAAIlC,UAAU,CAACmC,qBAAsB,GACrE,cAAc,GAAG,cAAc,IAEjCrC,OAAO,GAAG,+BAA+B,GAAG,mBAAmB,EAC9D;QACHiB,QAAQ,EAAEjB,OAAQ;QAAAa,QAAA,EAEjBb,OAAO,gBACNH,OAAA;UAAKiB,SAAS,EAAC,iCAAiC;UAACK,KAAK,EAAC,4BAA4B;UAACC,IAAI,EAAC,MAAM;UAACC,OAAO,EAAC,WAAW;UAAAR,QAAA,gBACjHhB,OAAA;YAAQiB,SAAS,EAAC,YAAY;YAACwB,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACC,CAAC,EAAC,IAAI;YAAClB,MAAM,EAAC,cAAc;YAACG,WAAW,EAAC;UAAG;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eACrGjC,OAAA;YAAMiB,SAAS,EAAC,YAAY;YAACM,IAAI,EAAC,cAAc;YAACM,CAAC,EAAC;UAAiH;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzK,CAAC,gBAENjC,OAAA;UAAKsB,KAAK,EAAC,4BAA4B;UAACL,SAAS,EAAC,SAAS;UAACO,OAAO,EAAC,WAAW;UAACD,IAAI,EAAC,cAAc;UAAAP,QAAA,eACjGhB,OAAA;YAAM4C,QAAQ,EAAC,SAAS;YAACf,CAAC,EAAC,4JAA4J;YAACgB,QAAQ,EAAC;UAAS;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1M;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC1B,EAAA,CA/FIN,SAAS;AAAA6C,EAAA,GAAT7C,SAAS;AAiGf,eAAeA,SAAS;AAAC,IAAA6C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}