{"ast": null, "code": "var _jsxFileName = \"E:\\\\kumaran\\\\botnexus\\\\client\\\\src\\\\components\\\\ChatInput.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChatInput = ({\n  onSendMessage,\n  loading,\n  conversationalFlow,\n  hybridFlow,\n  onClearChat\n}) => {\n  _s();\n  const [message, setMessage] = useState('');\n  const handleSubmit = e => {\n    e.preventDefault();\n    if (message.trim() && !loading) {\n      onSendMessage(message);\n      setMessage('');\n    }\n  };\n  const handleCancel = () => {\n    onSendMessage('cancel');\n    setMessage('');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      className: \"flex items-center border-t border-gray-200 p-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        value: message,\n        onChange: e => setMessage(e.target.value),\n        placeholder: conversationalFlow && conversationalFlow.isActive ? \"Type your answer or 'cancel' to exit the form...\" : hybridFlow && hybridFlow.isActive && hybridFlow.isConversationalPhase ? \"🔄 Type your answer or 'cancel' to exit the hybrid form...\" : \"Ask a question...\",\n        className: \"flex-1 p-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n        disabled: loading\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this), (conversationalFlow && conversationalFlow.isActive || hybridFlow && hybridFlow.isActive && hybridFlow.isConversationalPhase) && /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"button\",\n        onClick: handleCancel,\n        className: \"bg-red-500 hover:bg-red-600 text-white px-3 py-2 transition-colors\",\n        disabled: loading,\n        children: \"Cancel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        className: `bg-blue-500 text-white p-2 ${conversationalFlow && conversationalFlow.isActive || hybridFlow && hybridFlow.isActive && hybridFlow.isConversationalPhase ? 'rounded-r-md' : 'rounded-r-md'} ${loading ? 'opacity-50 cursor-not-allowed' : 'hover:bg-blue-600'}`,\n        disabled: loading,\n        children: loading ? /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"animate-spin h-5 w-5 text-white\",\n          xmlns: \"http://www.w3.org/2000/svg\",\n          fill: \"none\",\n          viewBox: \"0 0 24 24\",\n          children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n            className: \"opacity-25\",\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            stroke: \"currentColor\",\n            strokeWidth: \"4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n            className: \"opacity-75\",\n            fill: \"currentColor\",\n            d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"svg\", {\n          xmlns: \"http://www.w3.org/2000/svg\",\n          className: \"h-5 w-5\",\n          viewBox: \"0 0 20 20\",\n          fill: \"currentColor\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            fillRule: \"evenodd\",\n            d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.707l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z\",\n            clipRule: \"evenodd\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 5\n  }, this);\n};\n_s(ChatInput, \"l8KXAebGu4sZHsyCIQX7P8si41w=\");\n_c = ChatInput;\nexport default ChatInput;\nvar _c;\n$RefreshReg$(_c, \"ChatInput\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "ChatInput", "onSendMessage", "loading", "conversationalFlow", "hybridFlow", "onClearChat", "_s", "message", "setMessage", "handleSubmit", "e", "preventDefault", "trim", "handleCancel", "children", "onSubmit", "className", "type", "value", "onChange", "target", "placeholder", "isActive", "isConversationalPhase", "disabled", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "xmlns", "fill", "viewBox", "cx", "cy", "r", "stroke", "strokeWidth", "d", "fillRule", "clipRule", "_c", "$RefreshReg$"], "sources": ["E:/kumaran/botnexus/client/src/components/ChatInput.js"], "sourcesContent": ["import React, { useState } from 'react';\r\n\r\nconst ChatInput = ({ onSendMessage, loading, conversationalFlow, hybridFlow, onClearChat }) => {\r\n  const [message, setMessage] = useState('');\r\n\r\n  const handleSubmit = (e) => {\r\n    e.preventDefault();\r\n    if (message.trim() && !loading) {\r\n      onSendMessage(message);\r\n      setMessage('');\r\n    }\r\n  };\r\n\r\n  const handleCancel = () => {\r\n    onSendMessage('cancel');\r\n    setMessage('');\r\n  };\r\n\r\n  return (\r\n    <div>      \r\n      {/* Input form */}\r\n      <form onSubmit={handleSubmit} className=\"flex items-center border-t border-gray-200 p-4\">\r\n        <input\r\n          type=\"text\"\r\n          value={message}\r\n          onChange={(e) => setMessage(e.target.value)}\r\n          placeholder={\r\n            (conversationalFlow && conversationalFlow.isActive) ? \r\n              \"Type your answer or 'cancel' to exit the form...\" : \r\n            (hybridFlow && hybridFlow.isActive && hybridFlow.isConversationalPhase) ? \r\n              \"🔄 Type your answer or 'cancel' to exit the hybrid form...\" : \r\n              \"Ask a question...\"\r\n          }\r\n          className=\"flex-1 p-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n          disabled={loading}\r\n        />\r\n        {/* Show cancel button when in conversational flow or hybrid flow conversational phase */}\r\n        {((conversationalFlow && conversationalFlow.isActive) || \r\n          (hybridFlow && hybridFlow.isActive && hybridFlow.isConversationalPhase)) && (\r\n          <button\r\n            type=\"button\"\r\n            onClick={handleCancel}\r\n            className=\"bg-red-500 hover:bg-red-600 text-white px-3 py-2 transition-colors\"\r\n            disabled={loading}\r\n          >\r\n            Cancel\r\n          </button>\r\n        )}\r\n        <button\r\n          type=\"submit\"\r\n          className={`bg-blue-500 text-white p-2 ${\r\n            (conversationalFlow && conversationalFlow.isActive) || \r\n            (hybridFlow && hybridFlow.isActive && hybridFlow.isConversationalPhase) ? \r\n              'rounded-r-md' : 'rounded-r-md'\r\n          } ${\r\n            loading ? 'opacity-50 cursor-not-allowed' : 'hover:bg-blue-600'\r\n          }`}\r\n          disabled={loading}\r\n        >\r\n          {loading ? (\r\n            <svg className=\"animate-spin h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n              <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\r\n              <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n            </svg>\r\n          ) : (\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n              <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.707l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z\" clipRule=\"evenodd\" />\r\n            </svg>\r\n          )}\r\n        </button>\r\n      </form>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ChatInput;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,SAAS,GAAGA,CAAC;EAAEC,aAAa;EAAEC,OAAO;EAAEC,kBAAkB;EAAEC,UAAU;EAAEC;AAAY,CAAC,KAAK;EAAAC,EAAA;EAC7F,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EAE1C,MAAMY,YAAY,GAAIC,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAIJ,OAAO,CAACK,IAAI,CAAC,CAAC,IAAI,CAACV,OAAO,EAAE;MAC9BD,aAAa,CAACM,OAAO,CAAC;MACtBC,UAAU,CAAC,EAAE,CAAC;IAChB;EACF,CAAC;EAED,MAAMK,YAAY,GAAGA,CAAA,KAAM;IACzBZ,aAAa,CAAC,QAAQ,CAAC;IACvBO,UAAU,CAAC,EAAE,CAAC;EAChB,CAAC;EAED,oBACET,OAAA;IAAAe,QAAA,eAEEf,OAAA;MAAMgB,QAAQ,EAAEN,YAAa;MAACO,SAAS,EAAC,gDAAgD;MAAAF,QAAA,gBACtFf,OAAA;QACEkB,IAAI,EAAC,MAAM;QACXC,KAAK,EAAEX,OAAQ;QACfY,QAAQ,EAAGT,CAAC,IAAKF,UAAU,CAACE,CAAC,CAACU,MAAM,CAACF,KAAK,CAAE;QAC5CG,WAAW,EACRlB,kBAAkB,IAAIA,kBAAkB,CAACmB,QAAQ,GAChD,kDAAkD,GACnDlB,UAAU,IAAIA,UAAU,CAACkB,QAAQ,IAAIlB,UAAU,CAACmB,qBAAqB,GACpE,4DAA4D,GAC5D,mBACH;QACDP,SAAS,EAAC,oGAAoG;QAC9GQ,QAAQ,EAAEtB;MAAQ;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC,EAED,CAAEzB,kBAAkB,IAAIA,kBAAkB,CAACmB,QAAQ,IACjDlB,UAAU,IAAIA,UAAU,CAACkB,QAAQ,IAAIlB,UAAU,CAACmB,qBAAsB,kBACvExB,OAAA;QACEkB,IAAI,EAAC,QAAQ;QACbY,OAAO,EAAEhB,YAAa;QACtBG,SAAS,EAAC,oEAAoE;QAC9EQ,QAAQ,EAAEtB,OAAQ;QAAAY,QAAA,EACnB;MAED;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT,eACD7B,OAAA;QACEkB,IAAI,EAAC,QAAQ;QACbD,SAAS,EAAE,8BACRb,kBAAkB,IAAIA,kBAAkB,CAACmB,QAAQ,IACjDlB,UAAU,IAAIA,UAAU,CAACkB,QAAQ,IAAIlB,UAAU,CAACmB,qBAAsB,GACrE,cAAc,GAAG,cAAc,IAEjCrB,OAAO,GAAG,+BAA+B,GAAG,mBAAmB,EAC9D;QACHsB,QAAQ,EAAEtB,OAAQ;QAAAY,QAAA,EAEjBZ,OAAO,gBACNH,OAAA;UAAKiB,SAAS,EAAC,iCAAiC;UAACc,KAAK,EAAC,4BAA4B;UAACC,IAAI,EAAC,MAAM;UAACC,OAAO,EAAC,WAAW;UAAAlB,QAAA,gBACjHf,OAAA;YAAQiB,SAAS,EAAC,YAAY;YAACiB,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACC,CAAC,EAAC,IAAI;YAACC,MAAM,EAAC,cAAc;YAACC,WAAW,EAAC;UAAG;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eACrG7B,OAAA;YAAMiB,SAAS,EAAC,YAAY;YAACe,IAAI,EAAC,cAAc;YAACO,CAAC,EAAC;UAAiH;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzK,CAAC,gBAEN7B,OAAA;UAAK+B,KAAK,EAAC,4BAA4B;UAACd,SAAS,EAAC,SAAS;UAACgB,OAAO,EAAC,WAAW;UAACD,IAAI,EAAC,cAAc;UAAAjB,QAAA,eACjGf,OAAA;YAAMwC,QAAQ,EAAC,SAAS;YAACD,CAAC,EAAC,4JAA4J;YAACE,QAAQ,EAAC;UAAS;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1M;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACtB,EAAA,CAvEIN,SAAS;AAAAyC,EAAA,GAATzC,SAAS;AAyEf,eAAeA,SAAS;AAAC,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}