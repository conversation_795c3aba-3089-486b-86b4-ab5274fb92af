import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import api from '../utils/api';

// Global set to track executed auto-triggers across all component instances
const globalExecutedTriggers = new Set();

/**
 * Component for displaying records with Apply buttons for form linking
 */
const RecordDisplayWithActions = ({ 
  data, 
  formId, 
  formLinkingConfig, 
  onFormLinkTriggered,
  hideRecordDisplay = false,
  showOnlyButtons = false
}) => {
  const [loadingActions, setLoadingActions] = useState({});
  const [autoTriggerCountdown, setAutoTriggerCountdown] = useState({});
  const [autoTriggeredActions, setAutoTriggeredActions] = useState(new Set());
  const [triggerProcessed, setTriggerProcessed] = useState(false);
  const componentId = useRef(Math.random().toString(36).substr(2, 9));
  const processingRef = useRef(false);
  const dataHashRef = useRef('');
  const lastProcessedDataRef = useRef(null);

  // Check if a record should show apply buttons based on conditions
  const shouldShowApplyButton = (record, conditions = []) => {
    if (!conditions || conditions.length === 0) {
      return true; // Show by default if no conditions
    }
    
    // Filter out empty conditions (conditions with empty field names)
    const validConditions = conditions.filter(condition => 
      condition.field && condition.field.trim() !== ''
    );
    
    if (validConditions.length === 0) {
      return true; // Show button if no valid conditions
    }
    
    return validConditions.every(condition => {
      const fieldValue = record[condition.field];
      const conditionValue = condition.value;
      
      switch (condition.operator) {
        case 'equals':
          return fieldValue === conditionValue;
        case 'not_equals':
          return fieldValue !== conditionValue;
        case 'contains':
          return fieldValue && fieldValue.toString().includes(conditionValue);
        case 'not_contains':
          return !fieldValue || !fieldValue.toString().includes(conditionValue);
        case 'exists':
          return fieldValue !== undefined && fieldValue !== null && fieldValue !== '';
        case 'not_exists':
          return fieldValue === undefined || fieldValue === null || fieldValue === '';
        default:
          return true;
      }
    });
  };

  // Create a stable data hash to prevent unnecessary processing
  const dataHash = useMemo(() => {
    return JSON.stringify(data);
  }, [data]);

  // Reset trigger processed when data changes, but use a more specific key
  useEffect(() => {
    const hasDataChanged = dataHashRef.current !== dataHash;
    
    if (!hasDataChanged) {
      return; // Skip if data hasn't actually changed
    }
    
    console.log(`🔄 RecordDisplayWithActions [${componentId.current}]: Data changed, resetting trigger state`);
    dataHashRef.current = dataHash;
    lastProcessedDataRef.current = null;
    setTriggerProcessed(false);
    setAutoTriggeredActions(new Set());
    processingRef.current = false;
    
    // Clear any global triggers for this form to allow fresh triggers
    const currentFormTriggers = Array.from(globalExecutedTriggers).filter(key => key.startsWith(`${formId}-`));
    currentFormTriggers.forEach(trigger => {
      globalExecutedTriggers.delete(trigger);
      console.log(`🧹 [${componentId.current}] Cleared global trigger:`, trigger.substring(0, 50) + '...');
    });
    
    // Also clean up old global triggers (keep only recent ones to prevent memory leaks)
    if (globalExecutedTriggers.size > 100) {
      const triggerArray = Array.from(globalExecutedTriggers);
      globalExecutedTriggers.clear();
      // Keep only the last 50 triggers
      triggerArray.slice(-50).forEach(trigger => globalExecutedTriggers.add(trigger));
    }
  }, [dataHash, formId]);

  // Memoize processed records to prevent unnecessary recalculations
  const processedRecords = useMemo(() => {
    if (!data) return [];
    
    let records = [];
    if (Array.isArray(data)) {
      records = data;
    } else if (data && typeof data === 'object') {
      // Check common array field names
      const arrayFields = ['attendanceInfo', 'records', 'items', 'data', 'results'];
      
      for (const field of arrayFields) {
        if (data[field] && Array.isArray(data[field])) {
          records = data[field];
          break;
        }
      }
      
      // If no array found, treat the object as a single record
      if (records.length === 0) {
        records = [data];
      }
    }
    
    return records;
  }, [data]);

  // Auto-trigger function with improved duplicate prevention
  const executeAutoTrigger = useCallback(() => {
    // Skip if already processed this exact data
    if (lastProcessedDataRef.current === dataHash) {
      console.log(`⏭️ [${componentId.current}] Skipping - same data already processed`);
      return;
    }

    // Skip if no config or already processing
    if (!formLinkingConfig?.recordActions || processingRef.current || triggerProcessed) {
      return;
    }

    // Set processing flag to prevent concurrent executions
    processingRef.current = true;
    lastProcessedDataRef.current = dataHash;

    try {
      let hasExecutedAnyTrigger = false;

      formLinkingConfig.recordActions.forEach((action, actionIndex) => {
        if (hasExecutedAnyTrigger) return; // Only execute first matching trigger per data set

        if (action.autoTrigger?.enabled) {
          const delay = action.autoTrigger.delaySeconds || 2;
          
          // Auto-trigger for the first record that meets conditions
          const eligibleRecord = processedRecords.find(record => 
            shouldShowApplyButton(record, action.conditions)
          );
          
          if (eligibleRecord) {
            // Create a more robust unique key
            const recordHash = JSON.stringify(eligibleRecord);
            const configHash = JSON.stringify(formLinkingConfig);
            const uniqueActionKey = `${formId}-${recordHash}-${actionIndex}-${configHash.substring(0, 20)}`;
            
            // Check both local and global tracking to prevent duplicates
            const hasBeenTriggeredLocally = autoTriggeredActions.has(uniqueActionKey);
            const hasBeenTriggeredGlobally = globalExecutedTriggers.has(uniqueActionKey);
            
            
            // Use the unique action key for tracking to prevent duplicate API responses from triggering
            if (!hasBeenTriggeredLocally && !hasBeenTriggeredGlobally) {
              hasExecutedAnyTrigger = true;
              
              console.log(`� [${componentId.current}] Auto-triggering form link in ${delay} seconds...`);
              
              // Add to tracking sets BEFORE executing to prevent race conditions
              setAutoTriggeredActions(prev => new Set(prev).add(uniqueActionKey));
              globalExecutedTriggers.add(uniqueActionKey);
              
              // For immediate trigger when hideRecordDisplay is true
              if (hideRecordDisplay) {
                console.log(`🚀 [${componentId.current}] Executing immediate auto-trigger (hideRecordDisplay mode)`);
                
                handleApplyClick(eligibleRecord, actionIndex);
                setTriggerProcessed(true);
              } else {
                // Mark as processed to prevent duplicate triggers for countdown mode
                setTriggerProcessed(true);
                
                // Start countdown for normal display mode
                setAutoTriggerCountdown(prev => ({
                  ...prev,
                  [uniqueActionKey]: delay
                }));
                
                // Countdown timer
                const countdownInterval = setInterval(() => {
                  setAutoTriggerCountdown(prev => {
                    const newCount = prev[uniqueActionKey] - 1;
                    if (newCount <= 0) {
                      clearInterval(countdownInterval);
                      return { ...prev, [uniqueActionKey]: 0 };
                    }
                    return { ...prev, [uniqueActionKey]: newCount };
                  });
                }, 1000);
                
                // Auto-trigger after delay
                setTimeout(() => {
                  console.log('🚀 Executing auto-trigger now');
                  handleApplyClick(eligibleRecord, actionIndex);
                  clearInterval(countdownInterval);
                }, delay * 1000);
              }
            } else {
              console.log(`⏭️ [${componentId.current}] Action already auto-triggered, skipping`);
            }
          }
        }
      });
    } finally {
      // Always reset processing flag
      processingRef.current = false;
    }
  }, [formLinkingConfig, hideRecordDisplay, triggerProcessed, processedRecords, dataHash, autoTriggeredActions, formId]);

  // Auto-trigger effect with better dependency management
  useEffect(() => {
    // Only trigger if we have valid data and config
    if (!formLinkingConfig?.recordActions || !data || triggerProcessed) {
      return;
    }

    // Use a short timeout to debounce rapid state changes
    const timeoutId = setTimeout(() => {
      executeAutoTrigger();
    }, 50);

    return () => {
      clearTimeout(timeoutId);
    };
  }, [executeAutoTrigger, data, formLinkingConfig, triggerProcessed]);

  // Handle Apply button click - memoized to prevent unnecessary re-renders
  const handleApplyClick = useCallback(async (record, actionIndex = 0) => { 
    const actionKey = `${JSON.stringify(record)}-${actionIndex}`;
    
    // Prevent duplicate clicks
    if (loadingActions[actionKey]) {
      console.log('⏭️ Skipping duplicate click - already processing:', actionKey);
      return;
    }
    
    setLoadingActions(prev => ({ ...prev, [actionKey]: true }));

    try {
      // Get the button text for this action
      const action = formLinkingConfig?.recordActions?.[actionIndex];
      const buttonText = action?.buttonText || 'Apply';
    
      
      const response = await api.post(`/unifiedconfigs/${formId}/form-link`, {
        recordData: record,
        parentData: data,
        actionIndex: actionIndex,
        buttonText: buttonText // Send button text to backend
      });

      if (response.data.success) {
        console.log('✅ Form linking successful:', response.data);
        
        // Check if auto-submit on click is enabled (from backend response)
        const autoSubmitOnClick = response.data.autoSubmitOnClick || false;
        
        if (autoSubmitOnClick) {
          console.log('🚀 Auto-submitting linking form due to autoSubmitOnClick setting from backend');
          
          // Auto-submit the linking form with the same payload structure as normal submission
          await handleAutoSubmitLinkingForm(
            response.data.targetForm._id, 
            response.data.prefillData,
            response.data.targetForm.name,
            actionKey
          );
        } else {
          // Normal behavior - show the form
          console.log('📋 Displaying form for user input');
          
          // Call the callback with the target form and prefilled data
          if (onFormLinkTriggered) {
            onFormLinkTriggered({
              targetForm: response.data.targetForm,
              prefillData: response.data.prefillData,
              buttonText: response.data.buttonText,
              buttonStyle: response.data.buttonStyle
            });
          }
        }
      } else {
        console.error('❌ Form linking failed:', response.data.message);
        alert('Failed to open form: ' + response.data.message);
      }
    } catch (error) {
      console.error('❌ Error during form linking:', error);
      alert('Error opening form: ' + (error.response?.data?.message || error.message));
    } finally {
      setLoadingActions(prev => ({ ...prev, [actionKey]: false }));
    }
  }, [formLinkingConfig, formId, data, loadingActions, onFormLinkTriggered]);

  // Handle auto-submit linking form when autoSubmitOnClick is enabled
  const handleAutoSubmitLinkingForm = useCallback(async (targetFormId, prefillData, formName, actionKey) => {
    try {
      console.log('🚀 Auto-submitting linking form:', { targetFormId, prefillData, formName });
      
      // Get user data for headers (required by server)
      const userData = JSON.parse(localStorage.getItem('user') || '{}');
      let authToken = userData.token;
      
      // Prepare headers with user authentication data
      const headers = {
        'Authorization': authToken ? `Bearer ${authToken}` : undefined,
        'x-emp-id': userData.empId || undefined,
        'x-role-type': userData.roleType || undefined,
      };
      
  
      // Use the direct form submission API that doesn't require conversationId
      // This avoids the "Conversation not found" error
      const linkingFormPayload = {
        formData: prefillData
      };
      
      
      // Submit the linking form using the direct form submission endpoint
      const response = await api.post(`/unifiedconfigs/${targetFormId}/submit`, linkingFormPayload, { headers });
      
      console.log('✅ Linking form auto-submit response received:', response.data);
      
      if (response.data.success) {
        // Show success notification
        alert(`✅ ${formName} submitted successfully!`);
        console.log('🎉 Linking form auto-submitted successfully!', response.data);
      } else {
        console.error('❌ Linking form auto-submit failed:', {
          message: response.data.message,
          error: response.data.error,
          fullResponse: response.data
        });
        alert(`❌ Failed to submit ${formName}: ${response.data.message || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('❌ Error auto-submitting linking form:', error);
      alert(`❌ Error submitting ${formName}: ${error.response?.data?.message || error.message}`);
    } finally {
      // Clear loading state
      setLoadingActions(prev => ({ ...prev, [actionKey]: false }));
    }
  }, []);

  // Get button style classes
  const getButtonClasses = (style, isLoading) => {
    const baseClasses = 'px-3 py-1 rounded text-sm font-medium transition-colors duration-200';
    
    if (isLoading) {
      return `${baseClasses} bg-gray-400 text-white cursor-not-allowed`;
    }

    switch (style) {
      case 'primary':
        return `${baseClasses} bg-blue-500 hover:bg-blue-600 text-white`;
      case 'secondary':
        return `${baseClasses} bg-gray-500 hover:bg-gray-600 text-white`;
      case 'success':
        return `${baseClasses} bg-green-500 hover:bg-green-600 text-white`;
      case 'warning':
        return `${baseClasses} bg-yellow-500 hover:bg-yellow-600 text-white`;
      case 'danger':
        return `${baseClasses} bg-red-500 hover:bg-red-600 text-white`;
      default:
        return `${baseClasses} bg-blue-500 hover:bg-blue-600 text-white`;
    }
  };

  // Format field value for display
  const formatFieldValue = (value) => {
    if (value === null || value === undefined) return '-';
    if (typeof value === 'boolean') return value ? 'Yes' : 'No';
    if (typeof value === 'object') return JSON.stringify(value);
    return value.toString();
  };

  // Render records based on data structure
  const renderRecords = () => {
    let records = [];

    // Handle different data structures
    if (Array.isArray(data)) {
      records = data;
    } else if (data && typeof data === 'object') {
      // Check common array field names
      const arrayFields = ['attendanceInfo', 'records', 'items', 'data', 'results'];
      
      for (const field of arrayFields) {
        if (data[field] && Array.isArray(data[field])) {
          records = data[field];
          break;
        }
      }
      
      // If no array found, treat the object as a single record
      if (records.length === 0) {
        records = [data];
      }
    }

    if (records.length === 0) {
      return (
        <div className="text-gray-500 text-center py-4">
          No records to display
        </div>
      );
    }

    return records.map((record, index) => (
      <div key={index} className="mb-4">
        {/* Record Header */}
        <div className="mb-2">
          <span className="font-medium text-gray-800">Record {index + 1}:</span>
        </div>
        
        {/* Record Fields - Single line format */}
        <div className="mb-3 text-sm text-gray-700 bg-gray-50 p-3 rounded-lg">
          {(() => {
            // Check if this is leave data by looking for leaveTypeName and balance fields
            const isLeaveData = record.hasOwnProperty('leaveTypeName') && record.hasOwnProperty('balance');
            
            let fieldsToDisplay;
            if (isLeaveData) {
              // For leave data, only show leaveTypeName and balance
              fieldsToDisplay = [
                ['leaveTypeName', record.leaveTypeName],
                ['balance', record.balance]
              ];
            } else {
              // For other data, show all fields
              fieldsToDisplay = Object.entries(record);
            }
            
            return fieldsToDisplay.map(([key, value], fieldIndex) => (
              <span key={key}>
                {fieldIndex > 0 && ' '}
                <span className="font-medium">{key}:</span> {formatFieldValue(value)}
                {fieldIndex < fieldsToDisplay.length - 1 && ' '}
              </span>
            ));
          })()}
        </div>
        
        {/* Action buttons */}
        {formLinkingConfig?.enabled && formLinkingConfig.recordActions && (
          <div className="flex flex-wrap gap-2 mb-2">
            {formLinkingConfig.recordActions.map((action, actionIndex) => {
          
              if (!shouldShowApplyButton(record, action.conditions)) return null;
              
              const actionKey = `${JSON.stringify(record)}-${actionIndex}`;
              const isLoading = loadingActions[actionKey];
              const countdownValue = autoTriggerCountdown[actionKey];
              
              return (
                <div key={actionIndex} className="flex items-center gap-2">
                  {/* Only show Apply button if auto-trigger is disabled */}
                  {!action.autoTrigger?.enabled && (
                    <button
                      onClick={() => handleApplyClick(record, actionIndex)}
                      disabled={isLoading}
                      className={getButtonClasses(action.buttonStyle, isLoading)}
                    >
                      {isLoading ? (
                        <span className="flex items-center">
                          <svg className="animate-spin -ml-1 mr-1 h-3 w-3 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Loading...
                        </span>
                      ) : (
                        action.buttonText || 'Apply'
                      )}
                    </button>
                  )}
                  
                  {/* Auto-trigger countdown - only show if auto-trigger is enabled */}
                  {countdownValue > 0 && action.autoTrigger?.enabled && (
                    <div className="flex items-center text-sm text-blue-600">
                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      Auto-opening in {countdownValue}s
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        )}
      </div>
    ));
  };

  // If hideRecordDisplay is true, return null to only handle auto-trigger functionality
  if (hideRecordDisplay) {
    return null;
  }

  // If showOnlyButtons is true, render only action buttons without record details
  if (showOnlyButtons) {
    let records = [];
    if (Array.isArray(data)) {
      records = data;
    } else if (data && typeof data === 'object') {
      const arrayFields = ['attendanceInfo', 'records', 'items', 'data', 'results'];
      for (const field of arrayFields) {
        if (data[field] && Array.isArray(data[field])) {
          records = data[field];
          break;
        }
      }
      if (records.length === 0) {
        records = [data];
      }
    }

    return (
      <div className="space-y-4">
        {records.map((record, index) => (
          <div key={index}>
            {/* Action buttons only */}
            {formLinkingConfig?.enabled && formLinkingConfig.recordActions && (
              <div className="flex flex-wrap gap-2">
                {formLinkingConfig.recordActions.map((action, actionIndex) => {
                  if (!shouldShowApplyButton(record, action.conditions)) return null;
                  
                  const actionKey = `${JSON.stringify(record)}-${actionIndex}`;
                  const isLoading = loadingActions[actionKey];
                  const countdownValue = autoTriggerCountdown[actionKey];
                  
                  return (
                    <div key={actionIndex} className="flex items-center gap-2">
                      {/* Only show Apply button if auto-trigger is disabled */}
                      {!action.autoTrigger?.enabled && (
                        <button
                          onClick={() => handleApplyClick(record, actionIndex)}
                          disabled={isLoading}
                          className={getButtonClasses(action.buttonStyle || 'primary', isLoading)}
                        >
                          {isLoading ? 'Loading...' : (action.buttonText || 'Apply')}
                        </button>
                      )}
                      
                      {/* Auto-trigger countdown display */}
                      {action.autoTrigger?.enabled && countdownValue > 0 && (
                        <div className="text-sm text-gray-600 bg-yellow-50 px-2 py-1 rounded">
                          Auto-applying in {countdownValue}s...
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {renderRecords()}
    </div>
  );
};

export default RecordDisplayWithActions;