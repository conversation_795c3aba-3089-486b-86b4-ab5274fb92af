[{"E:\\kumaran\\botnexus\\client\\src\\index.js": "1", "E:\\kumaran\\botnexus\\client\\src\\reportWebVitals.js": "2", "E:\\kumaran\\botnexus\\client\\src\\App.js": "3", "E:\\kumaran\\botnexus\\client\\src\\context\\FormContext.js": "4", "E:\\kumaran\\botnexus\\client\\src\\context\\ApiConfigContext.js": "5", "E:\\kumaran\\botnexus\\client\\src\\context\\AuthContext.js": "6", "E:\\kumaran\\botnexus\\client\\src\\context\\ChatContext.js": "7", "E:\\kumaran\\botnexus\\client\\src\\pages\\ApiConfigPage.js": "8", "E:\\kumaran\\botnexus\\client\\src\\pages\\FormsPage.js": "9", "E:\\kumaran\\botnexus\\client\\src\\pages\\ChatPage.js": "10", "E:\\kumaran\\botnexus\\client\\src\\components\\ChatbotWidget.js": "11", "E:\\kumaran\\botnexus\\client\\src\\pages\\UnifiedConfigPage.js": "12", "E:\\kumaran\\botnexus\\client\\src\\pages\\DocumentsPage.js": "13", "E:\\kumaran\\botnexus\\client\\src\\layouts\\index.js": "14", "E:\\kumaran\\botnexus\\client\\src\\components\\ApiConfigForm.js": "15", "E:\\kumaran\\botnexus\\client\\src\\components\\FormBuilder.js": "16", "E:\\kumaran\\botnexus\\client\\src\\components\\ChatInterface.js": "17", "E:\\kumaran\\botnexus\\client\\src\\components\\UnifiedConfigBuilder.js": "18", "E:\\kumaran\\botnexus\\client\\src\\utils\\api.js": "19", "E:\\kumaran\\botnexus\\client\\src\\layouts\\MainLayout.js": "20", "E:\\kumaran\\botnexus\\client\\src\\layouts\\SimpleLayout.js": "21", "E:\\kumaran\\botnexus\\client\\src\\components\\ChatInput.js": "22", "E:\\kumaran\\botnexus\\client\\src\\components\\HybridForm.js": "23", "E:\\kumaran\\botnexus\\client\\src\\components\\AttendanceRegularizationDisplay.js": "24", "E:\\kumaran\\botnexus\\client\\src\\components\\TypingIndicator.js": "25", "E:\\kumaran\\botnexus\\client\\src\\components\\ChatMessage.js": "26", "E:\\kumaran\\botnexus\\client\\src\\components\\ChatFormDisplay.js": "27", "E:\\kumaran\\botnexus\\client\\src\\components\\JsonEditor.js": "28", "E:\\kumaran\\botnexus\\client\\src\\components\\FormLinkingConfig.js": "29", "E:\\kumaran\\botnexus\\client\\src\\components\\PayloadPreview.js": "30", "E:\\kumaran\\botnexus\\client\\src\\components\\RecordDisplayWithActions.js": "31", "E:\\kumaran\\botnexus\\client\\src\\components\\AutoTriggerHandler.js": "32", "E:\\kumaran\\botnexus\\client\\src\\components\\DynamicForm.js": "33"}, {"size": 552, "mtime": 1753416831573, "results": "34", "hashOfConfig": "35"}, {"size": 375, "mtime": 1753416831576, "results": "36", "hashOfConfig": "35"}, {"size": 2669, "mtime": 1753416831565, "results": "37", "hashOfConfig": "35"}, {"size": 5495, "mtime": 1753416831573, "results": "38", "hashOfConfig": "35"}, {"size": 4486, "mtime": 1753416831573, "results": "39", "hashOfConfig": "35"}, {"size": 5212, "mtime": 1753416831573, "results": "40", "hashOfConfig": "35"}, {"size": 39883, "mtime": 1753426379547, "results": "41", "hashOfConfig": "35"}, {"size": 9914, "mtime": 1753416831575, "results": "42", "hashOfConfig": "35"}, {"size": 6682, "mtime": 1753416831576, "results": "43", "hashOfConfig": "35"}, {"size": 288, "mtime": 1753416831575, "results": "44", "hashOfConfig": "35"}, {"size": 1687, "mtime": 1753416831569, "results": "45", "hashOfConfig": "35"}, {"size": 19531, "mtime": 1753416831576, "results": "46", "hashOfConfig": "35"}, {"size": 38815, "mtime": 1753416831575, "results": "47", "hashOfConfig": "35"}, {"size": 112, "mtime": 1753416831574, "results": "48", "hashOfConfig": "35"}, {"size": 16914, "mtime": 1753416831566, "results": "49", "hashOfConfig": "35"}, {"size": 54076, "mtime": 1753416831570, "results": "50", "hashOfConfig": "35"}, {"size": 25758, "mtime": 1753416831568, "results": "51", "hashOfConfig": "35"}, {"size": 87165, "mtime": 1753416831572, "results": "52", "hashOfConfig": "35"}, {"size": 1583, "mtime": 1753416831577, "results": "53", "hashOfConfig": "35"}, {"size": 1729, "mtime": 1753416831574, "results": "54", "hashOfConfig": "35"}, {"size": 247, "mtime": 1753416831574, "results": "55", "hashOfConfig": "35"}, {"size": 3176, "mtime": 1753416831567, "results": "56", "hashOfConfig": "35"}, {"size": 12339, "mtime": 1753416831571, "results": "57", "hashOfConfig": "35"}, {"size": 8356, "mtime": 1753416831566, "results": "58", "hashOfConfig": "35"}, {"size": 698, "mtime": 1753416831572, "results": "59", "hashOfConfig": "35"}, {"size": 22441, "mtime": 1753420192010, "results": "60", "hashOfConfig": "35"}, {"size": 38935, "mtime": 1753416831567, "results": "61", "hashOfConfig": "35"}, {"size": 2943, "mtime": 1753416831571, "results": "62", "hashOfConfig": "35"}, {"size": 24078, "mtime": 1753416831570, "results": "63", "hashOfConfig": "35"}, {"size": 9253, "mtime": 1753416831571, "results": "64", "hashOfConfig": "35"}, {"size": 23637, "mtime": 1753416831571, "results": "65", "hashOfConfig": "35"}, {"size": 7072, "mtime": 1753416831567, "results": "66", "hashOfConfig": "35"}, {"size": 12337, "mtime": 1753416831569, "results": "67", "hashOfConfig": "35"}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "2rg5jr", {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\kumaran\\botnexus\\client\\src\\index.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\reportWebVitals.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\App.js", ["167"], [], "E:\\kumaran\\botnexus\\client\\src\\context\\FormContext.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\context\\ApiConfigContext.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\context\\AuthContext.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\context\\ChatContext.js", ["168", "169", "170", "171", "172", "173", "174", "175", "176", "177", "178"], [], "E:\\kumaran\\botnexus\\client\\src\\pages\\ApiConfigPage.js", ["179"], [], "E:\\kumaran\\botnexus\\client\\src\\pages\\FormsPage.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\pages\\ChatPage.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\components\\ChatbotWidget.js", ["180", "181", "182", "183", "184"], [], "E:\\kumaran\\botnexus\\client\\src\\pages\\UnifiedConfigPage.js", ["185", "186"], [], "E:\\kumaran\\botnexus\\client\\src\\pages\\DocumentsPage.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\layouts\\index.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\components\\ApiConfigForm.js", ["187"], [], "E:\\kumaran\\botnexus\\client\\src\\components\\FormBuilder.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\components\\ChatInterface.js", ["188", "189"], [], "E:\\kumaran\\botnexus\\client\\src\\components\\UnifiedConfigBuilder.js", ["190", "191", "192"], [], "E:\\kumaran\\botnexus\\client\\src\\utils\\api.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\layouts\\MainLayout.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\layouts\\SimpleLayout.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\components\\ChatInput.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\components\\HybridForm.js", ["193"], [], "E:\\kumaran\\botnexus\\client\\src\\components\\AttendanceRegularizationDisplay.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\components\\TypingIndicator.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\components\\ChatMessage.js", ["194"], [], "E:\\kumaran\\botnexus\\client\\src\\components\\ChatFormDisplay.js", ["195", "196", "197", "198", "199", "200"], [], "E:\\kumaran\\botnexus\\client\\src\\components\\JsonEditor.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\components\\FormLinkingConfig.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\components\\PayloadPreview.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\components\\RecordDisplayWithActions.js", ["201", "202"], [], "E:\\kumaran\\botnexus\\client\\src\\components\\AutoTriggerHandler.js", ["203"], [], "E:\\kumaran\\botnexus\\client\\src\\components\\DynamicForm.js", [], [], {"ruleId": "204", "severity": 1, "message": "205", "line": 8, "column": 8, "nodeType": "206", "messageId": "207", "endLine": 8, "endColumn": 16}, {"ruleId": "204", "severity": 1, "message": "208", "line": 2, "column": 8, "nodeType": "206", "messageId": "207", "endLine": 2, "endColumn": 13}, {"ruleId": "204", "severity": 1, "message": "209", "line": 21, "column": 10, "nodeType": "206", "messageId": "207", "endLine": 21, "endColumn": 28}, {"ruleId": "210", "severity": 1, "message": "211", "line": 35, "column": 6, "nodeType": "212", "endLine": 35, "endColumn": 8, "suggestions": "213"}, {"ruleId": "204", "severity": 1, "message": "214", "line": 125, "column": 13, "nodeType": "206", "messageId": "207", "endLine": 125, "endColumn": 25}, {"ruleId": "215", "severity": 1, "message": "216", "line": 196, "column": 9, "nodeType": "217", "messageId": "218", "endLine": 196, "endColumn": 18}, {"ruleId": "215", "severity": 1, "message": "219", "line": 197, "column": 9, "nodeType": "217", "messageId": "218", "endLine": 197, "endColumn": 20}, {"ruleId": "215", "severity": 1, "message": "220", "line": 198, "column": 9, "nodeType": "217", "messageId": "218", "endLine": 198, "endColumn": 19}, {"ruleId": "215", "severity": 1, "message": "221", "line": 207, "column": 9, "nodeType": "217", "messageId": "218", "endLine": 207, "endColumn": 26}, {"ruleId": "222", "severity": 1, "message": "223", "line": 859, "column": 40, "nodeType": "224", "messageId": "218", "endLine": 859, "endColumn": 42}, {"ruleId": "222", "severity": 1, "message": "223", "line": 860, "column": 44, "nodeType": "224", "messageId": "218", "endLine": 860, "endColumn": 46}, {"ruleId": "204", "severity": 1, "message": "225", "line": 889, "column": 13, "nodeType": "206", "messageId": "207", "endLine": 889, "endColumn": 25}, {"ruleId": "204", "severity": 1, "message": "226", "line": 21, "column": 23, "nodeType": "206", "messageId": "207", "endLine": 21, "endColumn": 37}, {"ruleId": "204", "severity": 1, "message": "227", "line": 6, "column": 20, "nodeType": "206", "messageId": "207", "endLine": 6, "endColumn": 31}, {"ruleId": "204", "severity": 1, "message": "228", "line": 12, "column": 10, "nodeType": "206", "messageId": "207", "endLine": 12, "endColumn": 20}, {"ruleId": "204", "severity": 1, "message": "229", "line": 12, "column": 22, "nodeType": "206", "messageId": "207", "endLine": 12, "endColumn": 35}, {"ruleId": "204", "severity": 1, "message": "230", "line": 13, "column": 10, "nodeType": "206", "messageId": "207", "endLine": 13, "endColumn": 19}, {"ruleId": "204", "severity": 1, "message": "231", "line": 13, "column": 21, "nodeType": "206", "messageId": "207", "endLine": 13, "endColumn": 33}, {"ruleId": "210", "severity": 1, "message": "232", "line": 19, "column": 6, "nodeType": "212", "endLine": 19, "endColumn": 18, "suggestions": "233"}, {"ruleId": "204", "severity": 1, "message": "234", "line": 137, "column": 9, "nodeType": "206", "messageId": "207", "endLine": 137, "endColumn": 24}, {"ruleId": "235", "severity": 1, "message": "236", "line": 158, "column": 7, "nodeType": "237", "messageId": "238", "endLine": 179, "endColumn": 8}, {"ruleId": "204", "severity": 1, "message": "239", "line": 38, "column": 10, "nodeType": "206", "messageId": "207", "endLine": 38, "endColumn": 22}, {"ruleId": "204", "severity": 1, "message": "240", "line": 38, "column": 24, "nodeType": "206", "messageId": "207", "endLine": 38, "endColumn": 39}, {"ruleId": "204", "severity": 1, "message": "241", "line": 304, "column": 9, "nodeType": "206", "messageId": "207", "endLine": 304, "endColumn": 37}, {"ruleId": "204", "severity": 1, "message": "242", "line": 351, "column": 11, "nodeType": "206", "messageId": "207", "endLine": 351, "endColumn": 23}, {"ruleId": "204", "severity": 1, "message": "243", "line": 352, "column": 11, "nodeType": "206", "messageId": "207", "endLine": 352, "endColumn": 21}, {"ruleId": "204", "severity": 1, "message": "244", "line": 137, "column": 13, "nodeType": "206", "messageId": "207", "endLine": 137, "endColumn": 27}, {"ruleId": "204", "severity": 1, "message": "245", "line": 55, "column": 15, "nodeType": "206", "messageId": "207", "endLine": 55, "endColumn": 37}, {"ruleId": "204", "severity": 1, "message": "246", "line": 12, "column": 10, "nodeType": "206", "messageId": "207", "endLine": 12, "endColumn": 21}, {"ruleId": "204", "severity": 1, "message": "247", "line": 280, "column": 9, "nodeType": "206", "messageId": "207", "endLine": 280, "endColumn": 26}, {"ruleId": "204", "severity": 1, "message": "248", "line": 372, "column": 9, "nodeType": "206", "messageId": "207", "endLine": 372, "endColumn": 40}, {"ruleId": "204", "severity": 1, "message": "249", "line": 375, "column": 31, "nodeType": "206", "messageId": "207", "endLine": 375, "endColumn": 38}, {"ruleId": "204", "severity": 1, "message": "250", "line": 375, "column": 40, "nodeType": "206", "messageId": "207", "endLine": 375, "endColumn": 48}, {"ruleId": "204", "severity": 1, "message": "251", "line": 375, "column": 50, "nodeType": "206", "messageId": "207", "endLine": 375, "endColumn": 61}, {"ruleId": "210", "severity": 1, "message": "252", "line": 225, "column": 6, "nodeType": "212", "endLine": 225, "endColumn": 120, "suggestions": "253"}, {"ruleId": "210", "severity": 1, "message": "254", "line": 309, "column": 6, "nodeType": "212", "endLine": 309, "endColumn": 76, "suggestions": "255"}, {"ruleId": "204", "severity": 1, "message": "256", "line": 16, "column": 10, "nodeType": "206", "messageId": "207", "endLine": 16, "endColumn": 20}, "no-unused-vars", "'ChatPage' is defined but never used.", "Identifier", "unusedVar", "'axios' is defined but never used.", "'conversationLoaded' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'loadConversation' and 'loading'. Either include them or remove the dependency array.", "ArrayExpression", ["257"], "'supervisorId' is assigned a value but never used.", "no-dupe-keys", "Duplicate key 'fieldName'.", "ObjectExpression", "unexpected", "Duplicate key 'currentStep'.", "Duplicate key 'totalSteps'.", "Duplicate key 'isDynamicResponse'.", "eqeqeq", "Expected '!==' and instead saw '!='.", "BinaryExpression", "'errorMessage' is assigned a value but never used.", "'setTestResults' is assigned a value but never used.", "'setMessages' is assigned a value but never used.", "'inputValue' is assigned a value but never used.", "'setInputValue' is assigned a value but never used.", "'isLoading' is assigned a value but never used.", "'setIsLoading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchConfigs'. Either include it or remove the dependency array.", ["258"], "'handleCreateNew' is assigned a value but never used.", "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "'hybridFormId' is assigned a value but never used.", "'setHybridFormId' is assigned a value but never used.", "'handlePayloadStructureChange' is assigned a value but never used.", "'targetConfig' is assigned a value but never used.", "'targetPath' is assigned a value but never used.", "'successMessage' is assigned a value but never used.", "'hasDisabledAutoTrigger' is assigned a value but never used.", "'apiResponse' is assigned a value but never used.", "'prepareApiRequest' is assigned a value but never used.", "'prepareRegularizationApiRequest' is assigned a value but never used.", "'headers' is assigned a value but never used.", "'authType' is assigned a value but never used.", "'authDetails' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'handleApplyClick'. Either include it or remove the dependency array.", ["259"], "React Hook useCallback has a missing dependency: 'handleAutoSubmitLinkingForm'. Either include it or remove the dependency array.", ["260"], "'isChecking' is assigned a value but never used.", {"desc": "261", "fix": "262"}, {"desc": "263", "fix": "264"}, {"desc": "265", "fix": "266"}, {"desc": "267", "fix": "268"}, "Update the dependencies array to be: [loadConversation, loading]", {"range": "269", "text": "270"}, "Update the dependencies array to be: [fetchConfigs, filterType]", {"range": "271", "text": "272"}, "Update the dependencies array to be: [dataHash, formLinkingConfig, triggerProcessed, processedRecords, formId, autoTriggeredActions, hideRecordDisplay, handleApplyClick]", {"range": "273", "text": "274"}, "Update the dependencies array to be: [loadingActions, formLinkingConfig?.recordActions, formId, data, handleAutoSubmitLinkingForm, onFormLinkTriggered]", {"range": "275", "text": "276"}, [1527, 1529], "[loadConversation, loading]", [740, 752], "[fetchConfigs, filterType]", [9030, 9144], "[dataHash, formLinkingConfig, triggerProcessed, processedRecords, formId, autoTriggeredActions, hideRecordDisplay, handleApplyClick]", [12296, 12366], "[loadingActions, formLinkingConfig?.recordActions, formId, data, handleAutoSubmitLinkingForm, onFormLinkTriggered]"]